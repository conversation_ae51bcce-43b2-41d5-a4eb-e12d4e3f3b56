"""
LLM Provider Service - Dynamisk växling mellan OpenAI och Azure OpenAI
"""

import os
import logging
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod
import openai
from openai import OpenAI, AzureOpenAI

logger = logging.getLogger(__name__)


class LLMResponse:
    """Standardiserat svar från LLM providers"""

    def __init__(self, data: str, reasoning: str, success: bool = True, error: Optional[str] = None,
                 input_tokens: Optional[int] = None, output_tokens: Optional[int] = None):
        self.data = data
        self.reasoning = reasoning
        self.success = success
        self.error = error
        self.input_tokens = input_tokens
        self.output_tokens = output_tokens

    def to_dict(self) -> Dict[str, Any]:
        return {
            "data": self.data,
            "reasoning": self.reasoning,
            "success": self.success,
            "error": self.error,
            "input_tokens": self.input_tokens,
            "output_tokens": self.output_tokens
        }


class BaseLLMProvider(ABC):
    """Bas klass för LLM providers"""

    @abstractmethod
    async def send_prompt(self, prompt: str, system_prompt: Optional[str] = None, image_data: Optional[str] = None, file_type: Optional[str] = None) -> LLMResponse:
        """Skicka prompt till LLM och få strukturerat svar

        Args:
            prompt: Text prompt
            system_prompt: System prompt (optional)
            image_data: Base64 encoded image data (optional)
            file_type: File type (pdf, jpg, png, etc.) (optional)
        """
        pass

    @abstractmethod
    def get_provider_name(self) -> str:
        """Returnera provider namn"""
        pass


class OpenAIProvider(BaseLLMProvider):
    """OpenAI provider implementation"""

    def __init__(self, api_key: str, model: str = "gpt-4"):
        self.client = OpenAI(api_key=api_key)
        self.model = model
        logger.info(f"Initialized OpenAI provider with model: {model}")

    async def send_prompt(self, prompt: str, system_prompt: Optional[str] = None, image_data: Optional[str] = None, file_type: Optional[str] = None) -> LLMResponse:
        """Skicka prompt till OpenAI med stöd för bilder"""
        try:
            messages = []

            # o1 och o4-modeller stöder inte system messages, så vi lägger till system prompt i user message
            if system_prompt and not (self.model.startswith("o1") or self.model.startswith("o4")):
                messages.append({"role": "system", "content": system_prompt})

            # Skapa user message med text och eventuell bild
            user_content = []

            # För o1 och o4-modeller, lägg till system prompt i början av user message
            if system_prompt and (self.model.startswith("o1") or self.model.startswith("o4")):
                prompt = f"System instructions: {system_prompt}\n\nUser request: {prompt}"

            # Lägg till text
            user_content.append({
                "type": "text",
                "text": prompt
            })

            # Lägg till bild om den finns och är en bildtyp
            if image_data and file_type and file_type.lower() in ['jpg', 'jpeg', 'png']:
                # Bestäm MIME type baserat på filtyp
                mime_type = f"image/{file_type.lower()}"
                if file_type.lower() == 'jpg':
                    mime_type = "image/jpeg"

                user_content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:{mime_type};base64,{image_data}",
                        "detail": "high"  # Använd high detail för bättre OCR
                    }
                })

            messages.append({
                "role": "user",
                "content": user_content
            })

            # Använd rätt parameter baserat på modell
            completion_params = {
                "model": self.model,
                "messages": messages,
                "temperature": 1,  # Låg temperatur för konsistenta resultat
            }

            # o1 och o4-modeller använder max_completion_tokens istället för max_tokens
            if self.model.startswith("o1") or self.model.startswith("o4"):
                completion_params["max_completion_tokens"] = 4000
            else:
                completion_params["max_tokens"] = 4000

            response = self.client.chat.completions.create(**completion_params)

            content = response.choices[0].message.content

            # Extrahera token-information från response
            input_tokens = None
            output_tokens = None
            if hasattr(response, 'usage') and response.usage:
                input_tokens = response.usage.prompt_tokens
                output_tokens = response.usage.completion_tokens

            # Försök att extrahera data och reasoning från svaret
            # Förväntar sig format: {"data": "...", "reasoning": "..."}
            try:
                import json
                parsed = json.loads(content)
                data = parsed.get("data", content)
                reasoning = parsed.get("reasoning", "No reasoning provided")
            except json.JSONDecodeError:
                # Om svaret inte är JSON, använd hela svaret som data
                data = content
                reasoning = "Response was not in expected JSON format"

            return LLMResponse(
                data=data,
                reasoning=reasoning,
                success=True,
                input_tokens=input_tokens,
                output_tokens=output_tokens
            )

        except Exception as e:
            logger.error(f"Error calling OpenAI: {e}")
            return LLMResponse(
                data="",
                reasoning="",
                success=False,
                error=str(e),
                input_tokens=None,
                output_tokens=None
            )

    def get_provider_name(self) -> str:
        return f"OpenAI ({self.model})"



class AzureOpenAIProvider(BaseLLMProvider):
    """Azure OpenAI provider implementation"""

    def __init__(self, api_key: str, endpoint: str, deployment_name: str, api_version: str = "2024-02-15-preview"):
        self.client = AzureOpenAI(
            api_key=api_key,
            azure_endpoint=endpoint,
            api_version=api_version
        )
        self.deployment_name = deployment_name
        self.api_version = api_version
        logger.info(f"Initialized Azure OpenAI provider with deployment: {deployment_name}")

    async def send_prompt(self, prompt: str, system_prompt: Optional[str] = None, image_data: Optional[str] = None, file_type: Optional[str] = None) -> LLMResponse:
        """Skicka prompt till Azure OpenAI med stöd för bilder"""
        try:
            messages = []

            # o1 och o4-modeller stöder inte system messages, så vi lägger till system prompt i user message
            if system_prompt and not (self.deployment_name.startswith("o1") or self.deployment_name.startswith("o4")):
                messages.append({"role": "system", "content": system_prompt})

            # Skapa user message med text och eventuell bild
            user_content = []

            # För o1 och o4-modeller, lägg till system prompt i början av user message
            if system_prompt and (self.deployment_name.startswith("o1") or self.deployment_name.startswith("o4")):
                prompt = f"System instructions: {system_prompt}\n\nUser request: {prompt}"

            # Lägg till text
            user_content.append({
                "type": "text",
                "text": prompt
            })

            # Lägg till bild om den finns och är en bildtyp
            if image_data and file_type and file_type.lower() in ['jpg', 'jpeg', 'png']:
                # Bestäm MIME type baserat på filtyp
                mime_type = f"image/{file_type.lower()}"
                if file_type.lower() == 'jpg':
                    mime_type = "image/jpeg"

                user_content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:{mime_type};base64,{image_data}",
                        "detail": "high"  # Använd high detail för bättre OCR
                    }
                })

            messages.append({
                "role": "user",
                "content": user_content
            })

            # Använd rätt parameter baserat på deployment name
            completion_params = {
                "model": self.deployment_name,  # Azure använder deployment name som model
                "messages": messages,
                "temperature": 1,
            }

            # o1 och o4-modeller använder max_completion_tokens istället för max_tokens
            if self.deployment_name.startswith("o1") or self.deployment_name.startswith("o4"):
                completion_params["max_completion_tokens"] = 4000
            else:
                completion_params["max_tokens"] = 4000

            response = self.client.chat.completions.create(**completion_params)

            content = response.choices[0].message.content

            # Extrahera token-information från response
            input_tokens = None
            output_tokens = None
            if hasattr(response, 'usage') and response.usage:
                input_tokens = response.usage.prompt_tokens
                output_tokens = response.usage.completion_tokens

            # Försök att extrahera data och reasoning från svaret
            try:
                import json
                parsed = json.loads(content)
                data = parsed.get("data", content)
                reasoning = parsed.get("reasoning", "No reasoning provided")
            except json.JSONDecodeError:
                data = content
                reasoning = "Response was not in expected JSON format"

            return LLMResponse(
                data=data,
                reasoning=reasoning,
                success=True,
                input_tokens=input_tokens,
                output_tokens=output_tokens
            )

        except Exception as e:
            logger.error(f"Error calling Azure OpenAI: {e}")
            return LLMResponse(
                data="",
                reasoning="",
                success=False,
                error=str(e),
                input_tokens=None,
                output_tokens=None
            )

    def get_provider_name(self) -> str:
        return f"Azure OpenAI ({self.deployment_name})"

class LLMProviderService:
    """Service för att hantera LLM providers dynamiskt"""

    def __init__(self):
        self.provider: Optional[BaseLLMProvider] = None
        self._initialize_provider()

    def _initialize_provider(self):
        """Initialisera provider baserat på miljövariabler"""
        provider_type = os.getenv("LLM_PROVIDER", "openai").lower()

        if provider_type == "azure":
            self._initialize_azure_provider()
        elif provider_type == "openai":
            self._initialize_openai_provider()
        else:
            raise ValueError(f"Unsupported LLM provider: {provider_type}")

    def _initialize_openai_provider(self):
        """Initialisera OpenAI provider"""
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required for OpenAI provider")

        model = os.getenv("OPENAI_MODEL", "o4-mini")
        logger.info(f"Initializing OpenAI provider with model: {model}")
        self.provider = OpenAIProvider(api_key=api_key, model=model)
        logger.info(f"LLM Provider initialized: OpenAI with model {model}")

    def _initialize_azure_provider(self):
        """Initialisera Azure OpenAI provider"""
        api_key = os.getenv("AZURE_OPENAI_API_KEY")
        endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        deployment_name = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
        api_version = os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-15-preview")

        if not all([api_key, endpoint, deployment_name]):
            raise ValueError(
                "AZURE_OPENAI_API_KEY, AZURE_OPENAI_ENDPOINT, and AZURE_OPENAI_DEPLOYMENT_NAME "
                "environment variables are required for Azure OpenAI provider"
            )

        self.provider = AzureOpenAIProvider(
            api_key=api_key,
            endpoint=endpoint,
            deployment_name=deployment_name,
            api_version=api_version
        )
        logger.info("LLM Provider initialized: Azure OpenAI")

    async def send_prompt(self, prompt: str, system_prompt: Optional[str] = None, image_data: Optional[str] = None, file_type: Optional[str] = None) -> LLMResponse:
        """Skicka prompt till den konfigurerade providern"""
        if not self.provider:
            raise RuntimeError("LLM provider not initialized")

        return await self.provider.send_prompt(prompt, system_prompt, image_data, file_type)

    def get_provider_info(self) -> Dict[str, str]:
        """Få information om den aktiva providern"""
        if not self.provider:
            return {"provider": "Not initialized", "status": "error"}

        return {
            "provider": self.provider.get_provider_name(),
            "status": "active"
        }


# Global instance
_llm_service: Optional[LLMProviderService] = None


def get_llm_service() -> LLMProviderService:
    """Få global LLM service instance"""
    global _llm_service
    if _llm_service is None:
        _llm_service = LLMProviderService()
    return _llm_service


def reset_llm_service():
    """Reset global LLM service (för testing)"""
    global _llm_service
    _llm_service = None
